package com.jri.web.controller.biz.cus;


import com.jri.biz.cus.domain.request.CusSeaInventoryForm;
import com.jri.biz.cus.domain.vo.CusSeaInventoryVO;
import com.jri.biz.cus.service.CusSeaInventoryService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 保有量设置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Validated
@RestController
@RequestMapping("/cusSeaInventory")
@Api(tags = "保有量设置")
public class CusSeaInventoryController {
    @Resource
    private CusSeaInventoryService cusSeaInventoryService;

    @GetMapping("/getDetail")
    @ApiOperation("详情")
    public R<CusSeaInventoryVO> getDetailById() {
        return R.ok(cusSeaInventoryService.getDetailById());
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation("保存数据")
    public R<Boolean> save(@RequestBody @Valid CusSeaInventoryForm form) {
        return R.ok(cusSeaInventoryService.add(form));
    }
}

