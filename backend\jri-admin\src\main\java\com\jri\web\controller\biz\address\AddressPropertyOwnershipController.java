package com.jri.web.controller.biz.address;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.request.address.AddressPropertyOwnershipForm;
import com.jri.biz.domain.request.address.AddressPropertyOwnershipQuery;
import com.jri.biz.domain.vo.address.AddressPropertyOwnershipListVO;
import com.jri.biz.domain.vo.address.AddressPropertyOwnershipVO;
import com.jri.biz.service.address.AddressPropertyOwnershipService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 房屋产权证明 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Validated
@RestController
@RequestMapping("/addressPropertyOwnership")
@Api(tags = "房屋产权证明")
public class AddressPropertyOwnershipController {
    @Resource
    private AddressPropertyOwnershipService addressPropertyOwnershipService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<AddressPropertyOwnershipListVO>> listPage(AddressPropertyOwnershipQuery query) {
        return R.ok(addressPropertyOwnershipService.listPage(query));
    }


    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<AddressPropertyOwnershipVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(addressPropertyOwnershipService.getDetailById(id));
    }

    @PostMapping("/save")
    @ApiOperation("保存数据")
    public R<Void> save(@RequestBody @Valid AddressPropertyOwnershipForm form) {
        addressPropertyOwnershipService.add(form);
        return R.ok();
    }

    @PostMapping("/update")
    @ApiOperation("更新数据")
    public R<Void> update(@RequestBody @Valid AddressPropertyOwnershipForm form) {
        addressPropertyOwnershipService.update(form);
        return R.ok();
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Void> deleteById(Long id) {
        addressPropertyOwnershipService.deleteById(id);
        return R.ok();
    }

    @PostMapping("/enable")
    @ApiOperation("启用")
    public R<Void> enable(Long id) {
        addressPropertyOwnershipService.enable(id);
        return R.ok();
    }

    @GetMapping("/getIdlePropertyList")
    @ApiOperation("获取闲置房本")
    public R<List<AddressPropertyOwnershipListVO>> getIdlePropertyList(Long applyId) {
        return R.ok(addressPropertyOwnershipService.getIdlePropertyList(applyId));
    }

}

