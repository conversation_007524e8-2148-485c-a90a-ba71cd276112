package com.jri.web.controller.biz.address;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.request.address.AddressSupplierForm;
import com.jri.biz.domain.request.address.AddressSupplierQuery;
import com.jri.biz.domain.vo.address.AddressSupplierListVO;
import com.jri.biz.domain.vo.address.AddressSupplierVO;
import com.jri.biz.service.address.AddressSupplierService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 地址供应商 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
@Validated
@RestController
@RequestMapping("/addressSupplier")
@Api(tags = "地址供应商")
public class AddressSupplierController {
    @Resource
    private AddressSupplierService addressSupplierService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<AddressSupplierListVO>> listPage(AddressSupplierQuery query) {
        return R.ok(addressSupplierService.listPage(query));
    }

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<AddressSupplierVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(addressSupplierService.getDetailById(id));
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation("保存数据")
    public R<Boolean> saveOrUpdate(@RequestBody @Valid AddressSupplierForm form) {
        addressSupplierService.saveOrUpdate(form);
        return R.ok();
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(addressSupplierService.deleteById(id));
    }

    @PostMapping("/enable")
    @ApiOperation("状态设置")
    public R<Void> enable(@RequestParam("id") Long id) {
        addressSupplierService.enable(id);
        return R.ok();
    }
}

