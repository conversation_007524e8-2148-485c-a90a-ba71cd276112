package com.jri.web.controller.biz.cus;


import com.jri.biz.cus.domain.entity.CusCcFollow;
import com.jri.biz.cus.domain.request.CusCcFollowForm;
import com.jri.biz.cus.domain.request.CusCcFollowQuery;
import com.jri.biz.cus.service.CusCcFollowService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 跟进记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Validated
@RestController
@RequestMapping("/cusCcFollow")
@Api(tags = "跟进记录")
public class CusCcFollowController {
    @Resource
    private CusCcFollowService cusCcFollowService;

//    @GetMapping("/getById")
//    @ApiOperation("详情")
//    public R<CusCcFollowVO> getDetailById(@RequestParam("id") Long id) {
//        return R.ok(cusCcFollowService.getDetailById(id));
//    }

    @PostMapping("/save")
    @ApiOperation("保存数据")
    public R<Boolean> save(@RequestBody @Valid CusCcFollowForm form) {
        return R.ok(cusCcFollowService.add(form));
    }

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<List<CusCcFollow>> list(CusCcFollowQuery query) {
        return R.ok(cusCcFollowService.list(query));
    }

    @PostMapping("/saveBusiness")
    @ApiOperation("新增商机跟进记录")
    public R<Boolean> saveBusiness(@RequestBody @Valid CusCcFollowForm form) {
        return R.ok(cusCcFollowService.saveBusiness(form));
    }

    @GetMapping("/listBusiness")
    @ApiOperation("商机跟进记录列表查询")
    public R<List<CusCcFollow>> listBusiness(CusCcFollowQuery query) {
        return R.ok(cusCcFollowService.listBusiness(query));
    }

//    @PostMapping("/update")
//    @ApiOperation("更新数据")
//    public R<Boolean> update(@RequestBody @Valid CusCcFollowForm form) {
//        return R.ok(cusCcFollowService.update(form));
//    }
//
//    @DeleteMapping("/delete")
//    @ApiOperation("根据id删除")
//    public R<Boolean> deleteById(Long id) {
//        return R.ok(cusCcFollowService.deleteById(id));
//    }
}

