package com.jri.web.controller.biz.material;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.request.material.MaterialHandoverRecordForm;
import com.jri.biz.domain.request.material.MaterialHandoverRecordQuery;
import com.jri.biz.domain.request.material.MaterialHandoverRefuseForm;
import com.jri.biz.domain.vo.material.MaterialHandoverRecordListVO;
import com.jri.biz.domain.vo.material.MaterialHandoverRecordVO;
import com.jri.biz.service.material.MaterialHandoverRecordService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 材料交接记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
@Validated
@RestController
@RequestMapping("/materialHandoverRecord")
@Api(tags = "材料交接记录")
public class MaterialHandoverRecordController {
    @Resource
    private MaterialHandoverRecordService materialHandoverRecordService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<MaterialHandoverRecordListVO>> listPage(MaterialHandoverRecordQuery query) {
        return R.ok(materialHandoverRecordService.listPage(query));
    }

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<MaterialHandoverRecordVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(materialHandoverRecordService.getDetailById(id));
    }

    @PostMapping("/save")
    @ApiOperation("保存数据")
    public R<Boolean> save(@RequestBody @Valid MaterialHandoverRecordForm form) {
        materialHandoverRecordService.add(form);
        return R.ok();
    }

    @PostMapping("/accept")
    @ApiOperation("接收")
    public R<Boolean> accept(@RequestParam("id") Long id) {
        materialHandoverRecordService.accept(id);
        return R.ok();
    }

    @PostMapping("/refuse")
    @ApiOperation("拒绝")
    public R<Boolean> refuse(@RequestBody @Valid MaterialHandoverRefuseForm form) {
        materialHandoverRecordService.refuse(form);
        return R.ok();
    }

}

