package com.jri.web.controller.biz.cus;


import com.jri.biz.cus.domain.vo.CusCcRecordListVO;
import com.jri.biz.cus.service.CusCcRecordService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 操作记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Validated
@RestController
@RequestMapping("/cusCcRecord")
@Api(tags = "操作记录")
public class CusCcRecordController {
    @Resource
    private CusCcRecordService cusCcRecordService;

//    @GetMapping("/list")
//    @ApiOperation("列表查询")
//    public R<IPage<CusCcRecordListVO>> listPage(CusCcRecordQuery query) {
//        return R.ok(cusCcRecordService.listPage(query));
//    }

    @GetMapping("/listByCcId")
    @ApiOperation("查询操作记录列表")
    public R<List<CusCcRecordListVO>> listByCcId(@RequestParam("ccId") Long ccId) {
        return R.ok(cusCcRecordService.listByCcId(ccId));
    }

    @GetMapping("/businessListByCcId")
    @ApiOperation("查询操作记录列表(商机)")
    public R<List<CusCcRecordListVO>> businessListByCcId(@RequestParam("ccId") Long ccId) {
        return R.ok(cusCcRecordService.businessListByCcId(ccId));
    }

//    @GetMapping("/getById")
//    @ApiOperation("详情")
//    public R<CusCcRecordVO> getDetailById(@RequestParam("id") Long id) {
//        return R.ok(cusCcRecordService.getDetailById(id));
//    }
//
//    @PostMapping("/save")
//    @ApiOperation("保存数据")
//    public R<Boolean> save(@RequestBody @Valid CusCcRecordForm form) {
//        return R.ok(cusCcRecordService.add(form));
//    }
//
//    @PostMapping("/update")
//    @ApiOperation("更新数据")
//    public R<Boolean> update(@RequestBody @Valid CusCcRecordForm form) {
//        return R.ok(cusCcRecordService.update(form));
//    }
//
//    @DeleteMapping("/delete")
//    @ApiOperation("根据id删除")
//    public R<Boolean> deleteById(Long id) {
//        return R.ok(cusCcRecordService.deleteById(id));
//    }
}

