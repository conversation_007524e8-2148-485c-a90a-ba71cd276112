package com.jri.web.controller.biz.fund;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.constants.ProgressType;
import com.jri.biz.domain.common.ECharsPair;
import com.jri.biz.domain.request.fund.CollectionAnalysisQuery;
import com.jri.biz.domain.request.fund.MonthlyTurnoverQuery;
import com.jri.biz.domain.vo.CustomerBillDetailVO;
import com.jri.biz.domain.vo.fund.*;
import com.jri.biz.service.CustomerBillDetailService;
import com.jri.biz.service.ExportService;
import com.jri.biz.service.ProgressService;
import com.jri.biz.service.fund.TurnoverStatementService;
import com.jri.common.core.domain.R;
import com.jri.common.exception.ServiceException;
import com.jri.common.utils.StringUtils;
import com.jri.common.utils.poi.ExcelUtil;
import com.jri.common.utils.uuid.Seq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.Year;
import java.util.List;

@Validated
@RestController
@RequestMapping("/turnoverStatement")
@Api(tags = "月营业额明细")
@Slf4j
public class TurnoverStatementController {


    @Resource
    private TurnoverStatementService turnoverStatementService;

    @Resource
    private CustomerBillDetailService customerBillDetailService;

    @Resource
    private ProgressService progressService;

    @Resource
    private ExportService exportService;

    @GetMapping("/getMonthlyTurnoverDetail")
    @ApiOperation("月营业额明细列表查询")
    public R<IPage<MonthlyTurnoverDetailPreVo>> listPage(MonthlyTurnoverQuery query) {
        return R.ok(turnoverStatementService.getMonthlyTurnoverDetail(query));
    }

    @PostMapping("/getMonthlyTurnoverDetailExport")
    @ApiOperation("月营业额明细列表导出")
    public R<Long> listPageExport(@RequestBody MonthlyTurnoverQuery query) {
        String fileName = StringUtils.format("{}_{}", Seq.getId(Seq.uploadSeqType), "财款报表.xlsx");
        var id = progressService.create("download", fileName, ProgressType.DOWNLOAD);
        query.setPageNum(1);
        query.setPageSize(-1);
        exportService.exportTemplate(() -> turnoverStatementService.getMonthlyTurnoverDetailExport(query), fileName, id);
        return R.ok(id);
    }

    @GetMapping("/getMonthlyTurnoverDetailTab")
    @ApiOperation("月营业额明tab")
    public R<IPage<MonthlyTurnoverDetailPreChildVo>> getMonthlyTurnoverDetailTab(String customerNo,
                                                                                 String analysisMonth,
                                                                                 Long customerSuccessUserId,
                                                                                 Integer pageNum,
                                                                                 Integer pageSize) {
        MonthlyTurnoverQuery monthlyTurnoverQuery = new MonthlyTurnoverQuery();
        monthlyTurnoverQuery.setCustomerNo(customerNo);
        // 关联客户的用户id 包含财税顾问、客户成功、主办会计、开票员
        monthlyTurnoverQuery.setCustomerSuccessUserId(customerSuccessUserId);
        if (pageNum != null && pageSize != null) {
            monthlyTurnoverQuery.setPageNum(pageNum);
            monthlyTurnoverQuery.setPageSize(pageSize);
        }
        if (analysisMonth != null && !analysisMonth.isBlank()) {
            monthlyTurnoverQuery.setMonthlyTurnoverStart(analysisMonth);
            monthlyTurnoverQuery.setMonthlyTurnoverEnd(analysisMonth);
        }
        return R.ok(turnoverStatementService.getMonthlyTurnoverDetailTab(monthlyTurnoverQuery));
    }

    @GetMapping("/getCollectionAnalysis")
    @ApiOperation("回款分析列表查询")
    public R<IPage<CollectionAnalysisVo>> getCollectionAnalysis(CollectionAnalysisQuery query) {
        return R.ok(turnoverStatementService.getCollectionAnalysis(query));
    }

    @PostMapping("/getCollectionAnalysisExport")
    @ApiOperation("回款分析列表查询")
    public void getCollectionAnalysisExport(HttpServletResponse response, @RequestBody CollectionAnalysisQuery query) {
        List<CollectionAnalysisExportDTO> exportList = turnoverStatementService.getCollectionAnalysisExport(query);
        if (ObjectUtil.isEmpty(exportList)) {
            throw new ServiceException("没有数据可导出");
        }
        ExcelUtil<CollectionAnalysisExportDTO> util = new ExcelUtil<>(CollectionAnalysisExportDTO.class);
        util.exportExcel(response, exportList, "回款分析");
    }

    @GetMapping("/getCollectionAnalysisStatistic")
    @ApiOperation("回款分析统计")
    public R<CollectionAnalysisStatisticVO> getCollectionAnalysisStatistic(CollectionAnalysisQuery query) {
        return R.ok(turnoverStatementService.getCollectionAnalysisStatistic(query));
    }

    @GetMapping("/customerSuccessDetail")
    @ApiOperation("客户成功详情")
    public R<CollectionAnalysisVo> customerSuccessDetail(Long customerSuccessUserId,
                                                         String role) {
        return R.ok(turnoverStatementService.customerSuccessDetail(customerSuccessUserId, role));
    }

    @GetMapping("/monthlyCustomerSuccessTurnover")
    @ApiOperation("客户成功详情月营业额实收欠费折线图")
    public R<List<ECharsPair<String, MonthlyCustomerSuccessTurnover>>> monthlyCustomerSuccessTurnover(Long customerSuccessUserId,
                                                                                                      @DateTimeFormat(pattern = "yyyy") Year year,
                                                                                                      String role) {
        return R.ok(turnoverStatementService.monthlyCustomerSuccessTurnover(customerSuccessUserId, year, role));
    }

    @GetMapping("/detail")
    @ApiOperation("客户账单详情")
    public R<CustomerBillDetailVO> getBill(String customerNo) {
        return R.ok(customerBillDetailService.getBill(customerNo, null));
    }

    @GetMapping("/detailByCustomerId")
    @ApiOperation("客户账单详情")
    public R<CustomerBillDetailVO> getBill(Long customerId) {
        return R.ok(customerBillDetailService.getBill(null, customerId));
    }
}
