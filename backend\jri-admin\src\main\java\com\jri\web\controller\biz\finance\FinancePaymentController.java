package com.jri.web.controller.biz.finance;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.constants.ProgressType;
import com.jri.biz.domain.entity.PaymentImportDto;
import com.jri.biz.domain.request.finance.FinancePaymentCollectionAnalysisForm;
import com.jri.biz.domain.request.finance.FinancePaymentFeeTypeForm;
import com.jri.biz.domain.request.finance.FinancePaymentForm;
import com.jri.biz.domain.request.finance.FinancePaymentQuery;
import com.jri.biz.domain.vo.finance.FinancePaymentListVO;
import com.jri.biz.domain.vo.finance.FinancePaymentVO;
import com.jri.biz.domain.vo.finance.FinanceSettlementRecordVO;
import com.jri.biz.domain.vo.finance.FinanceStatisticVO;
import com.jri.biz.service.ExportService;
import com.jri.biz.service.ProgressService;
import com.jri.biz.service.finance.ContractChangeService;
import com.jri.biz.service.finance.FinancePaymentService;
import com.jri.biz.service.finance.ImportService;
import com.jri.common.annotation.Log;
import com.jri.common.core.domain.R;
import com.jri.common.enums.BusinessType;
import com.jri.common.exception.ServiceException;
import com.jri.common.utils.StringUtils;
import com.jri.common.utils.poi.NewExcelUtil;
import com.jri.common.utils.uuid.Seq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-14
 */
@Validated
@RestController
@RequestMapping("/financePayment")
@Api(tags = "账单")
public class FinancePaymentController {
    @Resource
    private FinancePaymentService financePaymentService;

    @Resource
    private ImportService importService;

    @Resource
    private ProgressService progressService;

    @Resource
    private ContractChangeService contractChangeService;

    @Resource
    private ExportService exportService;


    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<FinancePaymentListVO>> listPage(FinancePaymentQuery query) {
        return R.ok(financePaymentService.listPage(query));
    }

    @PostMapping("/export")
    @ApiOperation("导出")
    public R<Void> export(@Validated @RequestBody FinancePaymentQuery query) {
        String fileName = StringUtils.format("{}_{}", Seq.getId(Seq.uploadSeqType), "应收台账.xlsx");
        var id = progressService.create(ProgressType.CUSTOMER_DOWNLOAD, fileName, ProgressType.DOWNLOAD);
        exportService.paymentExport(fileName, query, id);
        return R.ok();
    }

    @GetMapping("/getStaticData")
    @ApiOperation("统计数据查询")
    public R<FinanceStatisticVO> getStaticData() {
        return R.ok(financePaymentService.getStaticData());
    }


    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<FinancePaymentVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(financePaymentService.getDetailById(id));
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation("保存数据")
    public R<Boolean> saveOrUpdate(@RequestBody @Valid FinancePaymentForm form) {
        return financePaymentService.saveOrUpdate(form);
    }

    @PostMapping("/collectionAnalysis")
    @ApiOperation("收款分析")
    public R<Boolean> collectionAnalysis(@RequestBody @Valid FinancePaymentCollectionAnalysisForm form) {
        return R.ok(financePaymentService.collectionAnalysis(form));
    }

    @PostMapping("/getPaymentamountByFeeType")
    @ApiOperation("根据费用类别去查询账款金额")
    public R<BigDecimal> getPaymentamountByFeeType(@RequestBody @Valid FinancePaymentFeeTypeForm form) {
        return R.ok(financePaymentService.getPaymentamountByFeeType(form));
    }

    @PostMapping("/update")
    @ApiOperation("更新数据")
    public R<Boolean> update(@RequestBody @Valid FinancePaymentForm form) {
        return R.ok(financePaymentService.update(form));
    }

    @Log(title = "账单", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Void> deleteById(Long id) {
        financePaymentService.deleteById(id);
        return R.ok();
    }


    @PostMapping("/createPaymentByContract")
    @ApiOperation("合同创建账单")
    public R<Boolean> createPaymentByContract(@RequestBody @Valid FinancePaymentForm form) {
        return financePaymentService.saveOrUpdate(form);
    }

    @PostMapping("/importPayment")
    @ApiOperation("导入账单")
    public R<Void> importPayment(@RequestPart("file") MultipartFile file) throws Exception {
        ImportService.fileCheck(file);
        NewExcelUtil<PaymentImportDto> excel = new NewExcelUtil<>(PaymentImportDto.class);
        List<PaymentImportDto> paymentImportDtoList = excel.importExcel(file.getInputStream(), excel.getExcelFieldNames().toArray(String[]::new));
        if (ObjectUtil.isEmpty(paymentImportDtoList)) {
            throw new ServiceException("导入内容为空");
        }
        var id = progressService.create(ProgressType.PAYMENT_UPLOAD, file.getOriginalFilename(), ProgressType.UPLOAD);
        importService.importPayment(paymentImportDtoList, id);
        return R.ok();
    }

    @PostMapping("/close")
    @ApiOperation("关闭账单")
    public R<Void> close(@RequestParam("id") Long id) {
        contractChangeService.close(id);
        return R.ok();
    }

    @GetMapping("/getSettlementInfo")
    @ApiOperation("获取结算信息")
    public R<List<FinanceSettlementRecordVO>> getSettlementInfo(@RequestParam("paymentId") Long paymentId) {
        return R.ok(contractChangeService.getSettlementInfo(paymentId));
    }

    @PostMapping("/updateOldPayment")
    @ApiOperation("下一个账单生成")
    public R<BigDecimal> updateOldPayment() {
        financePaymentService.updateOldPayment();
        return R.ok();
    }

}

