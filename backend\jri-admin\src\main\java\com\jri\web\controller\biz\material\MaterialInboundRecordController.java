package com.jri.web.controller.biz.material;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.constants.ProgressType;
import com.jri.biz.domain.request.material.MaterialInboundImportDto;
import com.jri.biz.domain.request.material.MaterialInboundRecordForm;
import com.jri.biz.domain.request.material.MaterialInboundRecordQuery;
import com.jri.biz.domain.vo.material.MaterialInboundRecordListVO;
import com.jri.biz.domain.vo.material.MaterialInboundRecordVO;
import com.jri.biz.service.ProgressService;
import com.jri.biz.service.finance.ImportService;
import com.jri.biz.service.material.MaterialInboundRecordService;
import com.jri.common.core.domain.R;
import com.jri.common.exception.ServiceException;
import com.jri.common.utils.poi.NewExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 材料入库记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
@Validated
@RestController
@RequestMapping("/materialInboundRecord")
@Api(tags = "材料入库记录")
public class MaterialInboundRecordController {

    @Resource
    private MaterialInboundRecordService materialInboundRecordService;

    @Resource
    private ProgressService progressService;

    @Resource
    private ImportService importService;


    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<MaterialInboundRecordListVO>> listPage(MaterialInboundRecordQuery query) {
        return R.ok(materialInboundRecordService.listPage(query));
    }

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<MaterialInboundRecordVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(materialInboundRecordService.getDetailById(id));
    }

    @PostMapping("/save")
    @ApiOperation("保存数据")
    public R<Boolean> save(@RequestBody @Valid MaterialInboundRecordForm form) {
        materialInboundRecordService.add(form);
        return R.ok();
    }

    @PostMapping("/importMaterial")
    @ApiOperation("导入材料")
    public R<Void> importMaterial(@RequestPart("file") MultipartFile file) throws Exception {
        ImportService.fileCheck(file);
        NewExcelUtil<MaterialInboundImportDto> excel = new NewExcelUtil<>(MaterialInboundImportDto.class);
        List<MaterialInboundImportDto> paymentImportDtoList = excel.importExcel(file.getInputStream(), excel.getExcelFieldNames().toArray(String[]::new));
        if (ObjectUtil.isEmpty(paymentImportDtoList)) {
            throw new ServiceException("导入内容为空");
        }
        var progressId = progressService.create(ProgressType.MATERIAL_UPLOAD, file.getOriginalFilename(), ProgressType.UPLOAD);
        importService.importMaterial(paymentImportDtoList, progressId);
        return R.ok();
    }

}

