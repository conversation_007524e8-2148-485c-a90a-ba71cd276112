package com.jri.web.controller.biz.finance;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.request.finance.FinanceReceiptAnalyseQuery;
import com.jri.biz.domain.vo.finance.FinanceReceiptAnalyseListVO;
import com.jri.biz.service.finance.FinanceReceiptAnalyseService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-18
 */
@Validated
@RestController
@RequestMapping("/financeReceiptAnalyse")
@Api(tags = "收款分析主表")
public class FinanceReceiptAnalyseController {
    @Resource
    private FinanceReceiptAnalyseService financeReceiptAnalyseService;

    @GetMapping("/list")
    @ApiOperation("收款分析主列表查询")
    public R<IPage<FinanceReceiptAnalyseListVO>> listPage(FinanceReceiptAnalyseQuery query) {
        return R.ok(financeReceiptAnalyseService.listPage(query));
    }


}

