package com.jri.web.controller.biz.cus;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.cus.domain.request.ClueAppealEndForm;
import com.jri.biz.cus.domain.request.ClueAppealRecordForm;
import com.jri.biz.cus.domain.request.ClueAppealRecordQuery;
import com.jri.biz.cus.domain.vo.ClueAppealRecordListVO;
import com.jri.biz.cus.domain.vo.ClueAppealRecordVO;
import com.jri.biz.cus.service.ClueAppealRecordService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 线索申诉记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-10
 */
@Validated
@RestController
@RequestMapping("/clueAppealRecord")
@Api(tags = "线索申诉记录表")
public class ClueAppealRecordController {
    @Resource
    private ClueAppealRecordService clueAppealRecordService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<ClueAppealRecordListVO>> listPage(ClueAppealRecordQuery query) {
        return R.ok(clueAppealRecordService.listPage(query));
    }


    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<ClueAppealRecordVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(clueAppealRecordService.getDetailById(id));
    }

    @PostMapping("/save")
    @ApiOperation("保存数据")
    public R<Boolean> save(@RequestBody @Valid ClueAppealRecordForm form) {
        return R.ok(clueAppealRecordService.add(form));
    }

    @PostMapping("/update")
    @ApiOperation("更新数据")
    public R<Boolean> update(@RequestBody @Valid ClueAppealEndForm form) {
        return R.ok(clueAppealRecordService.update(form));
    }

}

