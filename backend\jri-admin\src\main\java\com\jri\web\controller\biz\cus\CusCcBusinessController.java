package com.jri.web.controller.biz.cus;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.cus.domain.request.CusCcBusinessForm;
import com.jri.biz.cus.domain.request.CusCcBusinessMarkForm;
import com.jri.biz.cus.domain.request.CusCcBusinessQuery;
import com.jri.biz.cus.domain.vo.CusBusinessBizVO;
import com.jri.biz.cus.domain.vo.CusCcBusinessListVO;
import com.jri.biz.cus.domain.vo.CusCcBusinessVO;
import com.jri.biz.cus.domain.vo.CustomerBusinessListVO;
import com.jri.biz.cus.service.CusBusinessBizService;
import com.jri.biz.cus.service.CusCcBusinessService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 商机 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
@Validated
@RestController
@RequestMapping("/cusCcBusiness")
@Api(tags = "商机")
public class CusCcBusinessController {
    @Resource
    private CusCcBusinessService cusCcBusinessService;

    @Resource
    private CusBusinessBizService cusBusinessBizService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<CusCcBusinessListVO>> listPage(CusCcBusinessQuery query) {
        return R.ok(cusCcBusinessService.listPage(query));
    }

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<CusCcBusinessVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(cusCcBusinessService.getDetailById(id));
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation("保存数据")
    public R<Long> saveOrUpdate(@RequestBody @Valid CusCcBusinessForm form) {
        return cusCcBusinessService.saveOrUpdate(form);
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(cusCcBusinessService.deleteById(id));
    }

    @GetMapping("/listByCcId")
    @ApiOperation("查询商机列表")
    public R<List<CustomerBusinessListVO>> listByCcId(@RequestParam("ccId") Long ccId) {
        return R.ok(cusCcBusinessService.listByCcId(ccId));
    }

    @GetMapping("/getAllBiz")
    @ApiOperation("查询客户所有签约业务")
    public R<List<CusBusinessBizVO>> getAllBiz(@RequestParam("ccId") Long ccId) {
        return R.ok(cusBusinessBizService.getAllBiz(ccId));
    }

    @PostMapping("/mark")
    @ApiOperation("标记赢单")
    public R<Void> mark(@RequestBody @Valid CusCcBusinessMarkForm form) {
        cusCcBusinessService.mark(form);
        return R.ok();
    }
}

