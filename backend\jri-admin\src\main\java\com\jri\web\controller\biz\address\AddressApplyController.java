package com.jri.web.controller.biz.address;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.request.address.AddressApplyBindForm;
import com.jri.biz.domain.request.address.AddressApplyForm;
import com.jri.biz.domain.request.address.AddressApplyQuery;
import com.jri.biz.domain.vo.address.AddressApplyListVO;
import com.jri.biz.domain.vo.address.AddressApplyVO;
import com.jri.biz.service.address.AddressApplyService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 地址申请 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-01
 */
@Validated
@RestController
@RequestMapping("/addressApply")
@Api(tags = "地址申请")
public class AddressApplyController {
    @Resource
    private AddressApplyService addressApplyService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<AddressApplyListVO>> listPage(AddressApplyQuery query) {
        return R.ok(addressApplyService.listPage(query));
    }

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<AddressApplyVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(addressApplyService.getDetailById(id));
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation("保存数据")
    public R<Long> saveOrUpdate(@RequestBody @Valid AddressApplyForm form) {
        return R.ok(addressApplyService.saveOrUpdate(form));
    }

    @PostMapping("/bindProperty")
    @ApiOperation("绑定房本")
    public R<Void> bindProperty(@RequestBody AddressApplyBindForm form) {
        addressApplyService.bindProperty(form);
        return R.ok();
    }

    @PostMapping("/finish")
    @ApiOperation("结束使用")
    public R<Void> finish(Long id) {
        addressApplyService.finish(id);
        return R.ok();
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Void> deleteById(Long id) {
        addressApplyService.deleteById(id);
        return R.ok();
    }

}

