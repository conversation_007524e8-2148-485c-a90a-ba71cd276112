package com.jri.web.controller.biz.material;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.request.material.MaterialInboundRecordDetailQuery;
import com.jri.biz.domain.vo.material.MaterialInboundRecordDetailListVO;
import com.jri.biz.service.material.MaterialInboundRecordDetailService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 材料入库明细 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
@Validated
@RestController
@RequestMapping("/materialInboundRecordDetail")
@Api(tags = "材料入库明细")
public class MaterialInboundRecordDetailController {
    @Resource
    private MaterialInboundRecordDetailService materialInboundRecordDetailService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<MaterialInboundRecordDetailListVO>> listPage(MaterialInboundRecordDetailQuery query) {
        return R.ok(materialInboundRecordDetailService.listPage(query));
    }
}

