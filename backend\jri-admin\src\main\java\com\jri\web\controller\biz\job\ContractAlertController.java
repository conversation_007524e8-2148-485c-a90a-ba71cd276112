package com.jri.web.controller.biz.job;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.request.job.ContractAlertForm;
import com.jri.biz.domain.request.job.ContractAlertQuery;
import com.jri.biz.domain.vo.job.ContractAlertListVO;
import com.jri.biz.domain.vo.job.ContractAlertVO;
import com.jri.biz.service.job.ContractAlertService;
import com.jri.common.core.domain.R;
import com.jri.message.domain.query.ExpireDetailQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@Validated
@RestController
@RequestMapping("/contractAlert")
@Api(tags = "合同预警")
public class ContractAlertController {
    @Resource
    private ContractAlertService contractAlertService;

    @GetMapping("/list")
        @ApiOperation("列表查询")
        public R<IPage<ContractAlertListVO>> listPage(ExpireDetailQuery query) {
        return R.ok(contractAlertService.listPage(query));
    }

}

