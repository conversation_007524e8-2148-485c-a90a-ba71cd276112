package com.jri.web.controller.biz.cus;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.common.core.domain.R;

import javax.annotation.Resource;
import javax.validation.Valid;

import com.jri.biz.cus.service.CusCcTagsService;
import com.jri.biz.cus.domain.vo.CusCcTagsListVO;
import com.jri.biz.cus.domain.vo.CusCcTagsVO;
import com.jri.biz.cus.domain.request.CusCcTagsForm;
import com.jri.biz.cus.domain.request.CusCcTagsQuery;


import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 线索/客户标签 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Validated
@RestController
@RequestMapping("/cusCcTags")
@Api(tags = "线索/客户标签")
public class CusCcTagsController {
    @Resource
    private CusCcTagsService cusCcTagsService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<CusCcTagsListVO>> listPage(CusCcTagsQuery query) {
        return R.ok(cusCcTagsService.listPage(query));
    }


    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<CusCcTagsVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(cusCcTagsService.getDetailById(id));
    }

    @PostMapping("/save")
    @ApiOperation("保存数据")
    public R<Boolean> save(@RequestBody @Valid CusCcTagsForm form) {
        return R.ok(cusCcTagsService.add(form));
    }

    @PostMapping("/update")
    @ApiOperation("更新数据")
    public R<Boolean> update(@RequestBody @Valid CusCcTagsForm form) {
        return R.ok(cusCcTagsService.update(form));
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(cusCcTagsService.deleteById(id));
    }
}

