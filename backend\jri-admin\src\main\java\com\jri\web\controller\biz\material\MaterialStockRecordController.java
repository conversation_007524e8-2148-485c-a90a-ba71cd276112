package com.jri.web.controller.biz.material;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.request.material.MaterialDeleteRecordQuery;
import com.jri.biz.domain.request.material.MaterialStockCountSearchForm;
import com.jri.biz.domain.request.material.MaterialStockRecordQuery;
import com.jri.biz.domain.vo.material.MaterialDeleteRecordListVO;
import com.jri.biz.domain.vo.material.MaterialStockRecordListVO;
import com.jri.biz.domain.vo.material.MaterialStockRecordVO;
import com.jri.biz.service.material.MaterialDeleteRecordService;
import com.jri.biz.service.material.MaterialStockRecordService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 材料库存记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
@Validated
@RestController
@RequestMapping("/materialStockRecord")
@Api(tags = "材料库存记录")
public class MaterialStockRecordController {

    @Resource
    private MaterialStockRecordService materialStockRecordService;

    @Resource
    private MaterialDeleteRecordService materialDeleteRecordService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<MaterialStockRecordListVO>> listPage(MaterialStockRecordQuery query) {
        return R.ok(materialStockRecordService.listPage(query));
    }

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<MaterialStockRecordVO> getDetailById(@RequestParam("id") Long id,
                                                  @RequestParam(name = "mergeFlag", defaultValue = "true") Boolean mergeFlag) {
        return R.ok(materialStockRecordService.getDetailById(id, mergeFlag));
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Void> deleteById(Long id) {
        materialStockRecordService.deleteById(id);
        return R.ok();
    }

    @GetMapping("/getAvailableStockCount")
    @ApiOperation("获取可交接库存")
    public R<Integer> getAvailableStockCount(@Valid MaterialStockCountSearchForm form) {
        return R.ok(materialStockRecordService.getAvailableStockCount(form));
    }

    @GetMapping("/deleteRecordList")
    @ApiOperation("删除记录")
    public R<IPage<MaterialDeleteRecordListVO>> listPage(MaterialDeleteRecordQuery query) {
        return R.ok(materialDeleteRecordService.listPage(query));
    }
}

