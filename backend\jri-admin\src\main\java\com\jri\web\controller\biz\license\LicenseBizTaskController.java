package com.jri.web.controller.biz.license;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.constants.ProgressType;
import com.jri.biz.domain.request.license.*;
import com.jri.biz.domain.vo.license.LicenseBizTaskListVO;
import com.jri.biz.domain.vo.license.TaskDetailVO;
import com.jri.biz.service.ExportService;
import com.jri.biz.service.ProgressService;
import com.jri.biz.service.license.LicenseBizTaskService;
import com.jri.biz.validate.ConditionalValid;
import com.jri.common.core.domain.R;
import com.jri.common.utils.StringUtils;
import com.jri.common.utils.uuid.Seq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 工商办证记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Validated
@RestController
@RequestMapping("/licenseBizTask")
@Api(tags = "工商办证记录")
public class LicenseBizTaskController {
    @Resource
    private LicenseBizTaskService licenseBizTaskService;

    @Resource
    private ExportService exportService;

    @Resource
    private ProgressService progressService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<LicenseBizTaskListVO>> listPage(LicenseBizTaskQuery query) {
        return R.ok(licenseBizTaskService.listPage(query));
    }

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<TaskDetailVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(licenseBizTaskService.getDetailById(id));
    }

    @PostMapping("/save")
    @ApiOperation("保存数据")
    public R<Void> save(@RequestBody @Valid LicenseBizTaskForm form) {
        licenseBizTaskService.add(form);
        return R.ok();
    }

    @PostMapping("/assign")
    @ApiOperation("指派人员处理")
    public R<Void> assign(@RequestBody @Valid TaskAssignForm form) {
        licenseBizTaskService.assign(form);
        return R.ok();
    }

    @PostMapping("/delegate")
    @ApiOperation("转单")
    public R<Void> delegate(@RequestBody @Valid TaskAssignForm form) {
        licenseBizTaskService.delegate(form);
        return R.ok();
    }

    @PostMapping("/deprecate")
    @ApiOperation("废弃任务")
    public R<Void> deprecate(Long id) {
        licenseBizTaskService.deprecate(id);
        return R.ok();
    }

    @PostMapping("/dataCollection")
    @ApiOperation("数据收集阶段请求处理")
    public R<Void> dataCollection(@RequestBody @Valid LicenseBizDataCollectionForm form) {
        licenseBizTaskService.dataCollection(form);
        return R.ok();
    }

    @PostMapping("/dataCollectionComplete")
    @ApiOperation("数据收集阶段请求处理完成")
    public R<Void> dataCollectionComplete(@RequestBody @Validated(ConditionalValid.class) LicenseBizDataCollectionForm form) {
        form.setCompleteFlag(true);
        licenseBizTaskService.dataCollection(form);
        return R.ok();
    }

    @PostMapping("/processReport")
    @ApiOperation("办理进度汇报")
    public R<Void> processReport(@RequestBody @Valid ProcessReportForm form) {
        licenseBizTaskService.processReport(form);
        return R.ok();
    }

    @PostMapping("/processReportComplete")
    @ApiOperation("办理进度汇报完成")
    public R<Void> processReportComplete(@RequestBody @Validated(ConditionalValid.class) ProcessReportForm form) {
        form.setCompleteFlag(true);
        licenseBizTaskService.processReport(form);
        return R.ok();
    }

    @PostMapping("/dataUpload")
    @ApiOperation("信息上传")
    public R<Void> dataUpload(@RequestBody @Valid DataUploadForm form) {
        licenseBizTaskService.dataUpload(form);
        return R.ok();
    }

    @PostMapping("/dataUploadComplete")
    @ApiOperation("信息上传完成")
    public R<Void> dataUploadComplete(@RequestBody @Validated(ConditionalValid.class) DataUploadForm form) {
        form.setCompleteFlag(true);
        licenseBizTaskService.dataUpload(form);
        return R.ok();
    }

    @PostMapping("/bankAccountOpen")
    @ApiOperation("银行开户业务")
    public R<Void> bankAccountOpen(@RequestBody @Valid BankAccountOpenForm form) {
        licenseBizTaskService.bankAccountOpen(form);
        return R.ok();
    }

    @PostMapping("/bankAccountOpenComplete")
    @ApiOperation("银行开户业务")
    public R<Void> bankAccountOpenComplete(@RequestBody @Validated(ConditionalValid.class) BankAccountOpenForm form) {
        form.setCompleteFlag(true);
        licenseBizTaskService.bankAccountOpen(form);
        return R.ok();
    }

    @PostMapping("/accountantNotify")
    @ApiOperation("会计通知")
    public R<Void> accountantNotify(@RequestBody @Valid TaskAssignForm form) {
        licenseBizTaskService.accountantNotify(form);
        return R.ok();
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(licenseBizTaskService.deleteById(id));
    }

    @PostMapping("/permitDataCollection")
    @ApiOperation("数据收集阶段请求处理(许可证)")
    public R<Void> permitDataCollection(@RequestBody @Valid PermitLicenseBizDataCollectionForm form) {
        licenseBizTaskService.permitDataCollection(form);
        return R.ok();
    }

    @PostMapping("/permitDataCollectionComplete")
    @ApiOperation("数据收集阶段请求处理完成(许可证)")
    public R<Void> permitDataCollectionComplete(@RequestBody @Validated(ConditionalValid.class) PermitLicenseBizDataCollectionForm form) {
        form.setCompleteFlag(true);
        licenseBizTaskService.permitDataCollection(form);
        return R.ok();
    }

    @PostMapping("/permitProcessReport")
    @ApiOperation("办理进度汇报(许可证)")
    public R<Void> permitProcessReport(@RequestBody @Valid ProcessReportForm form) {
        licenseBizTaskService.permitProcessReport(form);
        return R.ok();
    }

    @PostMapping("/permitProcessReportComplete")
    @ApiOperation("办理进度汇报完成(许可证)")
    public R<Void> permitProcessReportComplete(@RequestBody @Validated(ConditionalValid.class) ProcessReportForm form) {
        form.setCompleteFlag(true);
        licenseBizTaskService.permitProcessReport(form);
        return R.ok();
    }

    @PostMapping("/permitDataUpload")
    @ApiOperation("信息上传(许可证)")
    public R<Void> permitDataUpload(@RequestBody @Valid PermitLicenseBizDataCollectionForm form) {
        licenseBizTaskService.permitDataUpload(form);
        return R.ok();
    }

    @PostMapping("/permitDataUploadComplete")
    @ApiOperation("信息上传完成(许可证)")
    public R<Void> permitDataUploadComplete(@RequestBody @Validated(ConditionalValid.class) PermitLicenseBizDataCollectionForm form) {
        form.setCompleteFlag(true);
        licenseBizTaskService.permitDataUpload(form);
        return R.ok();
    }

    @PostMapping("/businessCancellation")
    @ApiOperation("工商注销阶段请求处理")
    public R<Void> businessCancellation(@RequestBody @Valid CancellationCollectionForm form) {
        licenseBizTaskService.businessCancellation(form);
        return R.ok();
    }

    @PostMapping("/businessCancellationComplete")
    @ApiOperation("工商注销阶段请求处理完成")
    public R<Void> businessCancellationComplete(@RequestBody @Validated(ConditionalValid.class) CancellationCollectionForm form) {
        form.setCompleteFlag(true);
        licenseBizTaskService.businessCancellation(form);
        return R.ok();
    }

    @PostMapping("/businessCancellationProcessReport")
    @ApiOperation("银行注销")
    public R<Void> businessCancellationProcessReport(@RequestBody @Valid ProcessReportForm form) {
        licenseBizTaskService.businessCancellationProcessReport(form);
        return R.ok();
    }

    @PostMapping("/businessCancellationProcessReportComplete")
    @ApiOperation("银行注销完成")
    public R<Void> businessCancellationProcessReportComplete(@RequestBody @Validated(ConditionalValid.class) ProcessReportForm form) {
        form.setCompleteFlag(true);
        licenseBizTaskService.businessCancellationProcessReport(form);
        return R.ok();
    }

    @PostMapping("/changeCollection")
    @ApiOperation("工商变更 变更进度阶段请求处理")
    public R<Void> changeCollection(@RequestBody @Valid ChangeCollectionForm form) {
        licenseBizTaskService.changeCollection(form);
        return R.ok();
    }

    @PostMapping("/changeCollectionComplete")
    @ApiOperation("工商变更 变更进度阶段请求处理完成")
    public R<Void> changeCollectionComplete(@RequestBody @Validated(ConditionalValid.class) ChangeCollectionForm form) {
        form.setCompleteFlag(true);
        licenseBizTaskService.changeCollection(form);
        return R.ok();
    }

    @PostMapping("/dataUpdates")
    @ApiOperation("工商变更 资料更新阶段请求处理")
    public R<Void> dataUpdates(@RequestBody @Valid DataUpdatesCollectionForm form) {
        licenseBizTaskService.dataUpdates(form);
        return R.ok();
    }

    @PostMapping("/dataUpdatesComplete")
    @ApiOperation("工商变更 资料更新阶段请求处理完成")
    public R<Void> dataUpdatesComplete(@RequestBody @Validated(ConditionalValid.class) DataUpdatesCollectionForm form) {
        form.setCompleteFlag(true);
        licenseBizTaskService.dataUpdates(form);
        return R.ok();
    }

    @PostMapping("/export")
    @ApiOperation("导出")
    public R<Void> export(@Validated @RequestBody LicenseBizTaskQuery query) {
        String fileName = StringUtils.format("{}_{}", Seq.getId(Seq.uploadSeqType), "工商办证记录.xlsx");
        var id = progressService.create(ProgressType.LICENSE_BIZ_TASK_DOWNLOAD, fileName, ProgressType.DOWNLOAD);
        exportService.licenseBizTaskExport(fileName, query, id);
        return R.ok();
    }

}

