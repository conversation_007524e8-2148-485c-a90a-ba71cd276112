package com.jri.web.controller.biz.finance;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.request.finance.FinanceReceiptCheckQuery;
import com.jri.biz.domain.request.finance.FinanceReceiptForm;
import com.jri.biz.domain.request.finance.FinanceReceiptQuery;
import com.jri.biz.domain.vo.finance.FinanceAssociatedPaymentVO;
import com.jri.biz.domain.vo.finance.FinanceReceiptCheckListVO;
import com.jri.biz.domain.vo.finance.FinanceReceiptListVO;
import com.jri.biz.domain.vo.finance.FinanceReceiptVO;
import com.jri.biz.service.finance.FinanceReceiptService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-14
 */
@Validated
@RestController
@RequestMapping("/financeReceipt")
@Api(tags = "收款")
public class FinanceReceiptController {
    @Resource
    private FinanceReceiptService financeReceiptService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<FinanceReceiptListVO>> listPage(FinanceReceiptQuery query) {
        return R.ok(financeReceiptService.listPage(query));
    }

    @GetMapping("/getSubmitListPage")
    @ApiOperation("收款提交列表查询")
    public R<IPage<FinanceReceiptCheckListVO>> getSubmitListPage(FinanceReceiptCheckQuery query) {
        return R.ok(financeReceiptService.getSubmitListPage(query));
    }

    @PreAuthorize("@ss.hasPermi('finanace:receipt:check')")
    @GetMapping("/getCheckListPage")
    @ApiOperation("收款审核列表查询")
    public R<IPage<FinanceReceiptCheckListVO>> getCheckListPage(FinanceReceiptCheckQuery query) {
        return R.ok(financeReceiptService.getCheckListPage(query));
    }

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<FinanceReceiptVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(financeReceiptService.getDetailById(id));
    }


    @GetMapping("/getFinanceAssociatedPayment")
    @ApiOperation("根据关联账单查询账单")
    public R<FinanceAssociatedPaymentVO> getFinanceAssociatedPayment(@RequestParam("paymentId") Long paymentId) {
        return R.ok(financeReceiptService.getFinanceAssociatedPayment(paymentId));
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation("保存数据")
    public R<Boolean> saveOrUpdate(@RequestBody @Valid FinanceReceiptForm form) {
        return financeReceiptService.saveOrUpdate(form);
    }

    @PostMapping("/updateCheckStatus")
    @ApiOperation("审核状态修改")
    public R<Boolean> update(@RequestBody @Valid FinanceReceiptForm form) {
        return R.ok(financeReceiptService.update(form));
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(financeReceiptService.deleteById(id));
    }
}

