package com.jri.web.controller.biz.cus;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.common.core.domain.R;

import javax.annotation.Resource;
import javax.validation.Valid;

import com.jri.biz.cus.service.CusTagService;
import com.jri.biz.cus.domain.vo.CusTagListVO;
import com.jri.biz.cus.domain.vo.CusTagVO;
import com.jri.biz.cus.domain.request.CusTagForm;
import com.jri.biz.cus.domain.request.CusTagQuery;


import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 标签 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Validated
@RestController
@RequestMapping("/cusTag")
@Api(tags = "标签")
public class CusTagController {
    @Resource
    private CusTagService cusTagService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<CusTagListVO>> listPage(CusTagQuery query) {
        return R.ok(cusTagService.listPage(query));
    }

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<CusTagVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(cusTagService.getDetailById(id));
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation("保存数据")
    public R<Void> save(@RequestBody @Valid CusTagForm form) {
        cusTagService.add(form);
        return R.ok();
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(cusTagService.deleteById(id));
    }

    @PostMapping("/setStatus")
    @ApiOperation("状态设置")
    public R<Void> setStatus(@RequestParam("id") Long id) {
        cusTagService.setStatus(id);
        return R.ok();
    }
}

