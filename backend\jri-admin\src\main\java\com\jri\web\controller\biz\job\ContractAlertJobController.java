package com.jri.web.controller.biz.job;


import com.jri.biz.domain.request.job.ContractAlterJobForm;
import com.jri.biz.domain.request.job.ContractAlertJobQuery;
import com.jri.biz.domain.vo.job.ContractAlertJobListVO;
import com.jri.biz.domain.vo.job.ContractAlertJobVO;
import com.jri.biz.service.job.ContractAlertJobService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.common.core.domain.R;
import javax.annotation.Resource;
import javax.validation.Valid;


import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-25
 */
@Validated
@RestController
@RequestMapping("/contractAlterJob")
@Api(tags = "合同预警")
public class ContractAlertJobController {
    @Resource
    private ContractAlertJobService contractAlertJobService;

    @GetMapping("/list")
        @ApiOperation("列表查询")
        public R<IPage<ContractAlertJobListVO>> listPage(ContractAlertJobQuery query) {
        return R.ok(contractAlertJobService.listPage(query));
    }


    @GetMapping("/getById")
        @ApiOperation("详情")
        public R<ContractAlertJobVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(contractAlertJobService.getDetailById(id));
    }

    @PostMapping("/save")
        @ApiOperation("保存数据")
        public R<Boolean> save(@RequestBody @Valid ContractAlterJobForm form) {
        return contractAlertJobService.addOrUpdate(form);
    }

    @DeleteMapping("/delete")
        @ApiOperation("根据id删除")
        public R<Boolean> deleteById(Long id) {
        return R.ok(contractAlertJobService.deleteById(id));
    }

}

