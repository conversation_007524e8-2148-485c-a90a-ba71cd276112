package com.jri.web.controller.biz.cus;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.common.core.domain.R;

import javax.annotation.Resource;
import javax.validation.Valid;

import com.jri.biz.cus.service.CusCcShareService;
import com.jri.biz.cus.domain.vo.CusCcShareListVO;
import com.jri.biz.cus.domain.vo.CusCcShareVO;
import com.jri.biz.cus.domain.request.CusCcShareForm;
import com.jri.biz.cus.domain.request.CusCcShareQuery;


import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 线索/客户共享 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Validated
@RestController
@RequestMapping("/cusCcShare")
@Api(tags = "线索/客户共享")
public class CusCcShareController {
    @Resource
    private CusCcShareService cusCcShareService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<CusCcShareListVO>> listPage(CusCcShareQuery query) {
        return R.ok(cusCcShareService.listPage(query));
    }


    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<CusCcShareVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(cusCcShareService.getDetailById(id));
    }

    @PostMapping("/save")
    @ApiOperation("保存数据")
    public R<Boolean> save(@RequestBody @Valid CusCcShareForm form) {
        return R.ok(cusCcShareService.add(form));
    }

    @PostMapping("/update")
    @ApiOperation("更新数据")
    public R<Boolean> update(@RequestBody @Valid CusCcShareForm form) {
        return R.ok(cusCcShareService.update(form));
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(cusCcShareService.deleteById(id));
    }
}

