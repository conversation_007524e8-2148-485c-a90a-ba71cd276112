package com.jri.web.controller.biz.cus;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.common.core.domain.R;

import javax.annotation.Resource;
import javax.validation.Valid;

import com.jri.biz.cus.service.CusSeaService;
import com.jri.biz.cus.domain.vo.CusSeaListVO;
import com.jri.biz.cus.domain.vo.CusSeaVO;
import com.jri.biz.cus.domain.request.CusSeaForm;
import com.jri.biz.cus.domain.request.CusSeaQuery;


import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 公海配置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Validated
@RestController
@RequestMapping("/cusSea")
@Api(tags = "公海配置")
public class CusSeaController {
    @Resource
    private CusSeaService cusSeaService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<CusSeaListVO>> listPage(CusSeaQuery query) {
        return R.ok(cusSeaService.listPage(query));
    }

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<CusSeaVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(cusSeaService.getDetailById(id));
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation("保存数据")
    public R<Void> save(@RequestBody @Valid CusSeaForm form) {
        cusSeaService.add(form);
        return R.ok();
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(cusSeaService.deleteById(id));
    }

    @PostMapping("/setStatus")
    @ApiOperation("状态设置")
    public R<Void> setStatus(@RequestParam("id") Long id) {
        cusSeaService.setStatus(id);
        return R.ok();
    }
}

