package com.jri.web.controller.biz.cus;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.constants.ProgressType;
import com.jri.biz.cus.domain.request.*;
import com.jri.biz.cus.domain.vo.*;
import com.jri.biz.cus.service.CusCustomerOrClueService;
import com.jri.biz.service.ExportService;
import com.jri.biz.service.ProgressService;
import com.jri.biz.validate.ConditionalValid;
import com.jri.common.core.domain.R;
import com.jri.common.utils.StringUtils;
import com.jri.common.utils.uuid.Seq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 线索/客户信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Validated
@RestController
@RequestMapping("/cusCustomerOrClue")
@Api(tags = "线索/客户信息")
public class CusCustomerOrClueController {

    @Resource
    private CusCustomerOrClueService cusCustomerOrClueService;

    @Resource
    private ProgressService progressService;

    @Resource
    private ExportService exportService;

    @GetMapping("/allClueList")
    @ApiOperation("所有私海线索")
    public R<IPage<CusCustomerOrClueListVO>> allClueList(AllCusCustomerOrClueQuery query) {
        return R.ok(cusCustomerOrClueService.allClueList(query));
    }

    @PostMapping("/allClueListExport")
    @ApiOperation("跟进线索导出")
    public R<Long> allClueListExport(@RequestBody AllCusCustomerOrClueQuery query) {
        String fileName = StringUtils.format("{}_{}", Seq.getId(Seq.uploadSeqType), "跟进线索.xlsx");
        var id = progressService.create(ProgressType.CLUE_DOWNLOAD, fileName, ProgressType.DOWNLOAD);
        query.setPageNum(1);
        query.setPageSize(-1);
        exportService.exportTemplate(fileName,
                () -> cusCustomerOrClueService.allClueList(query).getRecords(),
                "跟进线索",
                CusCustomerOrClueListVO.class,
                id);
        return R.ok(id);
    }

    @GetMapping("/myClueList")
    @ApiOperation("我的线索")
    public R<IPage<CusCustomerOrClueListVO>> myClueList(CusCustomerOrClueQuery query) {
        return R.ok(cusCustomerOrClueService.myClueList(query));
    }

    @PostMapping("/myClueListExport")
    @ApiOperation("我的线索导出")
    public R<Long> myClueListExport(@RequestBody CusCustomerOrClueQuery query) {
        String fileName = StringUtils.format("{}_{}", Seq.getId(Seq.uploadSeqType), "我的线索.xlsx");
        var id = progressService.create(ProgressType.CLUE_DOWNLOAD, fileName, ProgressType.DOWNLOAD);
        query.setPageNum(1);
        query.setPageSize(-1);
        List<String> noShowList = List.of("currentUserName");
        exportService.exportTemplate(fileName,
                () -> cusCustomerOrClueService.myClueList(query).getRecords(),
                "我的线索",
                CusCustomerOrClueListVO.class,
                id,
                noShowList);
        return R.ok(id);
    }

    @GetMapping("/shareClueList")
    @ApiOperation("共享线索")
    public R<IPage<CusCustomerOrClueListVO>> shareClueList(CusCustomerOrClueQuery query) {
        return R.ok(cusCustomerOrClueService.shareClueList(query));
    }

    @PostMapping("/shareClueListExport")
    @ApiOperation("共享线索导出")
    public R<Long> shareClueListExport(@RequestBody CusCustomerOrClueQuery query) {
        String fileName = StringUtils.format("{}_{}", Seq.getId(Seq.uploadSeqType), "共享线索.xlsx");
        var id = progressService.create(ProgressType.CLUE_DOWNLOAD, fileName, ProgressType.DOWNLOAD);
        query.setPageNum(1);
        query.setPageSize(-1);
        exportService.exportTemplate(fileName,
                () -> cusCustomerOrClueService.shareClueList(query).getRecords(),
                "共享线索",
                CusCustomerOrClueListVO.class,
                id);
        return R.ok(id);
    }

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<CusCustomerOrClueVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(cusCustomerOrClueService.getDetailById(id));
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation("保存数据")
    public R<Void> save(@RequestBody @Valid CusCustomerOrClueForm form) {
        cusCustomerOrClueService.add(form);
        return R.ok();
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Void> deleteById(Long id) {
        cusCustomerOrClueService.deleteById(id);
        return R.ok();
    }

    @PostMapping("/updateTagsForm")
    @ApiOperation("修改标签")
    public R<Void> updateTagsForm(@RequestBody @Valid UpdateTagsForm form) {
        cusCustomerOrClueService.updateTagsForm(form);
        return R.ok();
    }

    @PostMapping("/ccChange")
    @ApiOperation("线索客户转让")
    public R<Void> ccChange(@RequestBody @Valid CcChangeForm form) {
        cusCustomerOrClueService.ccChange(form);
        return R.ok();
    }

    @PostMapping("/ccShare")
    @ApiOperation("线索共享")
    public R<Void> ccShare(@RequestBody @Valid CcShareForm form) {
        cusCustomerOrClueService.ccShare(form);
        return R.ok();
    }

    @GetMapping("/getUserIds")
    @ApiOperation("查询线索共享人id列表")
    public R<List<Long>> getUserIds(@RequestParam("ccId") Long ccId) {
        return R.ok(cusCustomerOrClueService.getUserIds(ccId));
    }

    @PostMapping("/ccRecovery")
    @ApiOperation("线索客户回收公海")
    public R<Void> ccRecovery(@RequestBody @Valid CcRecoveryForm form) {
        cusCustomerOrClueService.ccRecovery(form);
        return R.ok();
    }

    @PostMapping("/changeToCustomer")
    @ApiOperation("转为客户(新建客户)")
    public R<Void> changeToCustomer(@RequestBody @Valid CusCustomerOrClueForm form) {
        cusCustomerOrClueService.changeToCustomer(form);
        return R.ok();
    }

    @PostMapping("/clueToCustomer")
    @ApiOperation("转为客户(关联已有客户)")
    public R<Void> clueToCustomer(@RequestBody @Valid ClueToCustomerForm form) {
        cusCustomerOrClueService.clueToCustomer(form);
        return R.ok();
    }

    @GetMapping("/clueInSeaList")
    @ApiOperation("线索公海列表")
    public R<IPage<CusClueInSeaListVO>> clueInSeaList(CusClueInSeaListQuery query) {
        return R.ok(cusCustomerOrClueService.clueInSeaList(query));
    }

    @PostMapping("/clueInSeaListExport")
    @ApiOperation("线索公海列表导出")
    public R<Long> clueInSeaListExport(@RequestBody CusClueInSeaListQuery query) {
        String fileName = StringUtils.format("{}_{}", Seq.getId(Seq.uploadSeqType), "线索公海.xlsx");
        var id = progressService.create(ProgressType.CLUE_DOWNLOAD, fileName, ProgressType.DOWNLOAD);
        query.setPageNum(1);
        query.setPageSize(-1);
        exportService.exportTemplate(fileName,
                () -> cusCustomerOrClueService.clueInSeaList(query).getRecords(),
                "线索公海",
                CusClueInSeaListVO.class,
                id);
        return R.ok(id);
    }

    @PostMapping("/ccGet")
    @ApiOperation("领取线索或客户")
    public R<Void> ccGet(@RequestBody @Valid CcGetForm form) {
        cusCustomerOrClueService.ccGet(form);
        return R.ok();
    }

    @PostMapping("/ccDivide")
    @ApiOperation("分配线索或客户")
    public R<Void> ccDivide(@RequestBody @Valid CcDivideForm form) {
        cusCustomerOrClueService.ccDivide(form);
        return R.ok();
    }

    @PostMapping("/ccTransfer")
    @ApiOperation("线索客户转移公海")
    public R<Void> ccTransfer(@RequestBody @Valid CcTransferForm form) {
        cusCustomerOrClueService.ccTransfer(form);
        return R.ok();
    }

    @GetMapping("/allCustomerList")
    @ApiOperation("所有私海客户列表")
    public R<IPage<CusCustomerListVO>> allCustomerList(AllCusCustomerListQuery query) {
        return R.ok(cusCustomerOrClueService.allCustomerList(query));
    }

    @PostMapping("/allCustomerListExport")
    @ApiOperation("跟进客户列表导出")
    public R<Long> allCustomerListExport(@RequestBody AllCusCustomerListQuery query) {
        String fileName = StringUtils.format("{}_{}", Seq.getId(Seq.uploadSeqType), "跟进客户.xlsx");
        var id = progressService.create(ProgressType.CLUE_DOWNLOAD, fileName, ProgressType.DOWNLOAD);
        query.setPageNum(1);
        query.setPageSize(-1);
        exportService.exportTemplate(fileName,
                () -> {
                    List<CusCustomerListVO> cusCustomerListVOList = cusCustomerOrClueService.allCustomerList(query).getRecords();
                    // 商机分布处理
                    for (var vo : cusCustomerListVOList) {
                        vo.setOpportunityDistribution("赢单：" + vo.getWinNum() + " " +
                                "输单：" + vo.getLoseNum() + " " + "其他：" + vo.getOtherNum());
                        vo.setOpportunityNum(vo.getWinNum() + vo.getLoseNum() + vo.getOtherNum());
                    }
                    return cusCustomerListVOList;
                },
                "跟进客户",
                CusCustomerListVO.class,
                id);
        return R.ok(id);
    }

    @GetMapping("/customerList")
    @ApiOperation("客户列表")
    public R<IPage<CusCustomerListVO>> customerList(CusCustomerListQuery query) {
        return R.ok(cusCustomerOrClueService.customerList(query));
    }

    @PostMapping("/customerListExport")
    @ApiOperation("我的客户导出")
    public R<Long> customerListExport(@RequestBody CusCustomerListQuery query) {
        String fileName = StringUtils.format("{}_{}", Seq.getId(Seq.uploadSeqType), "我的客户.xlsx");
        var id = progressService.create(ProgressType.CLUE_DOWNLOAD, fileName, ProgressType.DOWNLOAD);
        query.setPageNum(1);
        query.setPageSize(-1);
        List<String> noShowList = List.of("currentUserName");
        exportService.exportTemplate(fileName,
                () -> {
                    List<CusCustomerListVO> cusCustomerListVOList = cusCustomerOrClueService.customerList(query).getRecords();
                    // 商机分布处理
                    for (var vo : cusCustomerListVOList) {
                        vo.setOpportunityDistribution("赢单：" + vo.getWinNum() + " " +
                                "输单：" + vo.getLoseNum() + " " + "其他：" + vo.getOtherNum());
                        vo.setOpportunityNum(vo.getWinNum() + vo.getLoseNum() + vo.getOtherNum());
                    }
                    return cusCustomerListVOList;
                },
                "我的客户",
                CusCustomerListVO.class,
                id,
                noShowList);
        return R.ok(id);
    }


    @PostMapping("/updateOtherInfo")
    @ApiOperation("编辑附加信息")
    public R<Void> updateOtherInfo(@RequestBody @Valid CusCustomerOtherInfoForm form) {
        cusCustomerOrClueService.updateOtherInfo(form);
        return R.ok();
    }

    @GetMapping("/getOtherInfo")
    @ApiOperation("查询附加信息")
    public R<CusCustomerOtherInfoForm> getOtherInfo(@RequestParam("id") Long id) {
        return R.ok(cusCustomerOrClueService.getOtherInfo(id));
    }

    @PostMapping("/establish")
    @ApiOperation("客户建档")
    public R<Long> establish(@RequestBody @Validated(ConditionalValid.class) EstablishForm form) {
        return cusCustomerOrClueService.establish(form);
    }

    @PostMapping("/associationEstablish")
    @ApiOperation("客户建档(关联已有客户)")
    public R<Long> associationEstablish(@RequestBody @Valid EstablishForm form) {
        return cusCustomerOrClueService.associationEstablish(form);
    }

    @GetMapping("/customerInSeaList")
    @ApiOperation("客户公海列表")
    public R<IPage<CusCustomerInSeaListVO>> customerInSeaList(CusCustomerListQuery query) {
        return R.ok(cusCustomerOrClueService.customerInSeaList(query));
    }

    @PostMapping("/customerInSeaListExport")
    @ApiOperation("客户公海列表导出")
    public R<Long> customerInSeaListExport(@RequestBody CusCustomerListQuery query) {
        String fileName = StringUtils.format("{}_{}", Seq.getId(Seq.uploadSeqType), "客户公海.xlsx");
        var id = progressService.create(ProgressType.CLUE_DOWNLOAD, fileName, ProgressType.DOWNLOAD);
        query.setPageNum(1);
        query.setPageSize(-1);
        exportService.exportTemplate(fileName,
                () -> cusCustomerOrClueService.customerInSeaList(query).getRecords(),
                "客户公海",
                CusCustomerInSeaListVO.class,
                id);
        return R.ok(id);
    }

    @GetMapping("/clueHeaderStatistic")
    @ApiOperation("线索管理页面头部统计")
    public R<ClueHeaderStatisticVO> clueHeaderStatisticVO() {
        return R.ok(cusCustomerOrClueService.clueHeaderStatisticVO());
    }

}

