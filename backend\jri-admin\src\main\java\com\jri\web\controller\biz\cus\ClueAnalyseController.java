package com.jri.web.controller.biz.cus;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.cus.domain.request.ClueAnalyseQuery;
import com.jri.biz.cus.domain.vo.*;
import com.jri.biz.cus.service.ClueAnalyseService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/1/3 8:53
 */
@Validated
@RestController
@RequestMapping("/clueAnalyse")
@Api(tags = "线索分析")
public class ClueAnalyseController {

    @Resource
    private ClueAnalyseService clueAnalyseService;

    @GetMapping("/channel")
    @ApiOperation("渠道来源分析")
    public R<IPage<BaseClueSourceAnalyseVO>> channel(ClueAnalyseQuery query) {
        return R.ok(clueAnalyseService.channelAnalyse(query));
    }

    @GetMapping("/customerIntroduction")
    @ApiOperation("客户介绍来源分析")
    public R<IPage<ClueSourceCustomerIntroductionAnalyseVO>> customerIntroduction(ClueAnalyseQuery query) {
        return R.ok(clueAnalyseService.customerIntroductionAnalyse(query));
    }

    @GetMapping("/direct")
    @ApiOperation("直投来源分析")
    public R<IPage<ClueSourceDirectAnalyseVO>> directAnalyse(ClueAnalyseQuery query) {
        return R.ok(clueAnalyseService.directAnalyse(query));
    }

    @GetMapping("/platform")
    @ApiOperation("平台来源分析")
    public R<IPage<ClueSourcePlatformAnalyseVO>> platformAnalyse(ClueAnalyseQuery query) {
        return R.ok(clueAnalyseService.platformAnalyse(query));
    }

    @GetMapping("/other")
    @ApiOperation("其他来源分析")
    public R<IPage<BaseClueSourceAnalyseVO>> other(ClueAnalyseQuery query) {
        return R.ok(clueAnalyseService.otherAnalyse(query));
    }

    @GetMapping("/staff/customerIntroduction")
    @ApiOperation("人员&客户介绍来源分析")
    public R<IPage<BaseStaffSourceAnalyseVO>> staffCustomerIntroductionAnalyse(ClueAnalyseQuery query) {
        return R.ok(clueAnalyseService.staffCustomerIntroductionAnalyse(query));
    }

    @GetMapping("/staff/channel")
    @ApiOperation("人员&渠道来源分析")
    public R<IPage<BaseStaffSourceAnalyseVO>> staffChannelAnalyse(ClueAnalyseQuery query) {
        return R.ok(clueAnalyseService.staffChannelAnalyse(query));
    }

    @GetMapping("/staff/other")
    @ApiOperation("人员&其他来源分析")
    public R<IPage<BaseStaffSourceAnalyseVO>> staffOtherAnalyse(ClueAnalyseQuery query) {
        return R.ok(clueAnalyseService.staffOtherAnalyse(query));
    }

    @GetMapping("/staff/all")
    @ApiOperation("人员&全部来源分析")
    public R<IPage<ClueStaffSourceAllAnalyseVO>> staffAllAnalyse(ClueAnalyseQuery query) {
        return R.ok(clueAnalyseService.staffAllAnalyse(query));
    }

    @GetMapping("/staff/direct")
    @ApiOperation("人员&直投来源分析")
    public R<IPage<ClueStaffSourceDirectAnalyseVO>> staffDirectAnalyse(ClueAnalyseQuery query) {
        return R.ok(clueAnalyseService.staffDirectAnalyse(query));
    }

    @GetMapping("/staff/platform")
    @ApiOperation("人员&平台来源分析")
    public R<IPage<ClueSourcePlatformAnalyseVO>> staffPlatformAnalyse(ClueAnalyseQuery query) {
        return R.ok(clueAnalyseService.staffPlatformAnalyse(query));
    }


}
