package com.jri.web.controller.biz.cus;


import com.jri.biz.cus.domain.request.CusCcContactBatchForm;
import com.jri.biz.cus.domain.vo.CusCcRecordListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.common.core.domain.R;

import javax.annotation.Resource;
import javax.validation.Valid;

import com.jri.biz.cus.service.CusCcContactService;
import com.jri.biz.cus.domain.vo.CusCcContactListVO;
import com.jri.biz.cus.domain.vo.CusCcContactVO;
import com.jri.biz.cus.domain.request.CusCcContactForm;
import com.jri.biz.cus.domain.request.CusCcContactQuery;


import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 联系人 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Validated
@RestController
@RequestMapping("/cusCcContact")
@Api(tags = "联系人")
public class CusCcContactController {
    @Resource
    private CusCcContactService cusCcContactService;

//    @GetMapping("/list")
//    @ApiOperation("列表查询")
//    public R<IPage<CusCcContactListVO>> listPage(CusCcContactQuery query) {
//        return R.ok(cusCcContactService.listPage(query));
//    }

    @GetMapping("/listByCcId")
    @ApiOperation("列表查询")
    public R<List<CusCcContactListVO>> listByCcId(@RequestParam("ccId") Long ccId) {
        return R.ok(cusCcContactService.listByCcId(ccId));
    }

    @PostMapping("/saveBatch")
    @ApiOperation("批量保存数据")
    public R<Void> saveBatch(@RequestBody @Valid CusCcContactBatchForm form) {
        cusCcContactService.saveBatch(form);
        return R.ok();
    }


//    @GetMapping("/getById")
//    @ApiOperation("详情")
//    public R<CusCcContactVO> getDetailById(@RequestParam("id") Long id) {
//        return R.ok(cusCcContactService.getDetailById(id));
//    }
//
//    @PostMapping("/save")
//    @ApiOperation("保存数据")
//    public R<Boolean> save(@RequestBody @Valid CusCcContactForm form) {
//        return R.ok(cusCcContactService.add(form));
//    }
//
//    @PostMapping("/update")
//    @ApiOperation("更新数据")
//    public R<Boolean> update(@RequestBody @Valid CusCcContactForm form) {
//        return R.ok(cusCcContactService.update(form));
//    }
//
//    @DeleteMapping("/delete")
//    @ApiOperation("根据id删除")
//    public R<Boolean> deleteById(Long id) {
//        return R.ok(cusCcContactService.deleteById(id));
//    }
}

