package com.jri.web.controller.biz.address;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.request.address.AddressLessorForm;
import com.jri.biz.domain.request.address.AddressLessorQuery;
import com.jri.biz.domain.vo.address.AddressLessorListVO;
import com.jri.biz.domain.vo.address.AddressLessorVO;
import com.jri.biz.service.address.AddressLessorService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 出租人 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Validated
@RestController
@RequestMapping("/addressLessor")
@Api(tags = "出租人")
public class AddressLessorController {
    @Resource
    private AddressLessorService addressLessorService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<AddressLessorListVO>> listPage(AddressLessorQuery query) {
        return R.ok(addressLessorService.listPage(query));
    }

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<AddressLessorVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(addressLessorService.getDetailById(id));
    }

    @PostMapping("/save")
    @ApiOperation("保存数据")
    public R<Boolean> save(@RequestBody @Valid AddressLessorForm form) {
        return R.ok(addressLessorService.add(form));
    }

    @PostMapping("/update")
    @ApiOperation("更新数据")
    public R<Boolean> update(@RequestBody @Valid AddressLessorForm form) {
        return R.ok(addressLessorService.update(form));
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(addressLessorService.deleteById(id));
    }
}

