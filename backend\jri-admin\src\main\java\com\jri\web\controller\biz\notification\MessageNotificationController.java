package com.jri.web.controller.biz.notification;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.vo.notification.MessageNotificationListVO;
import com.jri.biz.domain.vo.notification.MessageNotificationVO;
import com.jri.biz.service.notification.MessageNotificationService;
import com.jri.common.core.domain.R;
import com.jri.message.domain.query.MessageNotificationQuery;
import com.jri.message.domain.request.MessageNotificationForm;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03
 */
@Validated
@RestController
@RequestMapping("/messageNotification")
@Api(tags = "")
public class MessageNotificationController {
    @Resource
    private MessageNotificationService messageNotificationService;

    @GetMapping("/list")
        @ApiOperation("列表查询")
        public R<IPage<MessageNotificationListVO>> listPage(MessageNotificationQuery query) {
        return R.ok(messageNotificationService.listPage(query));
    }


    @GetMapping("/getById")
        @ApiOperation("详情")
        public R<MessageNotificationVO> getDetailById(@RequestParam("id") Long id) {
        return messageNotificationService.getDetailById(id);
    }

    @PostMapping("/save")
        @ApiOperation("保存数据")
        public R<Boolean> save(@RequestBody @Valid MessageNotificationForm form) {
        return R.ok(messageNotificationService.save(form));
    }


    @DeleteMapping("/delete")
        @ApiOperation("根据id删除")
        public R<Boolean> deleteById(Long id) {
        return R.ok(messageNotificationService.deleteById(id));
    }
}

