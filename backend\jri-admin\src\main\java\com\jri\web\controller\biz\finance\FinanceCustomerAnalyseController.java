package com.jri.web.controller.biz.finance;


import com.jri.biz.domain.entity.finance.FinanceCustomerAnalyse;
import com.jri.biz.domain.request.finance.*;
import com.jri.biz.domain.vo.finance.*;
import com.jri.biz.service.finance.FinanceCustomerAnalyseService;
import com.jri.biz.service.finance.FinanceReceiptAnalyseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.common.core.domain.R;

import javax.annotation.Resource;
import javax.validation.Valid;


import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Validated
@RestController
    @RequestMapping("/financeCustomerAnalyse")
@Api(tags = "收款分析和分析结果接口")
public class FinanceCustomerAnalyseController {
    @Resource
    private FinanceCustomerAnalyseService financeCustomerAnalyseService;


    @GetMapping("/list")
    @ApiOperation("新增时候列表查询")
    public R<IPage<FinanceCustomerAnalyseListVO>> listPage(FinanceCustomerAnalyseQuery query) {
        return R.ok(financeCustomerAnalyseService.listPage(query));
    }

    /**
     * 根据批次的id查询分析结果列表
     *
     * @param query
     * @return
     */
    @GetMapping("/getByAnalyseId")
    @ApiOperation("编辑时候列表查询")
    public R<IPage<FinanceCustomerAnalyseVO>> getByAnalyseId(FinanceCustomerAnalyseEditQuery query) {
        return R.ok(financeCustomerAnalyseService.getByAnalyseId(query));
    }


    @GetMapping("/analyseTabList")
    @ApiOperation("收款分析chart页tab查询")
    public R<FinanceAnalyseTabVO> getAnalyseTabList(FinanceReceiptAnalyseChartQuery query) {
        return R.ok(financeCustomerAnalyseService.getAnalyseTabList(query));
    }

    @GetMapping("/getSan")
    @ApiOperation("桑吉图查询")
    public R<FinanceSanVO> getSan(FinanceReceiptAnalyseChartQuery query) {
        return R.ok(financeCustomerAnalyseService.getSan(query));
    }


    @PostMapping("/save")
    @ApiOperation("新增")
    public R<Long> save(@RequestBody FinanceReceiptAnalyseForm form) {
        return R.ok(financeCustomerAnalyseService.add(form, null));
    }

    @PostMapping("/update")
    @ApiOperation("修改数据")
    public R<Long> update(@RequestBody @Valid FinanceCustomerAnalyse financeCustomerAnalyse) {
        if (financeCustomerAnalyse==null) {
            return R.ok(null, "没有更新任何数据");
        }
        return R.ok(financeCustomerAnalyseService.update(financeCustomerAnalyse));
    }

//    @PostMapping("/update")
//    @ApiOperation("修改数据")
//    public R<Boolean> update(@RequestBody @Valid List<FinanceCustomerAnalyse> financeCustomerAnalyse) {
//        if (financeCustomerAnalyse.size()==0) {
//            return R.ok(null);
//        }
//        return R.ok(financeCustomerAnalyseService.updateOld(financeCustomerAnalyse));
//    }

    @GetMapping("/updateStatus")
    @ApiOperation("关闭数据传batchId")
    public R<Boolean> updateStatus(Long batchId) {
        return R.ok(financeCustomerAnalyseService.updateStatus(batchId));
    }


}

