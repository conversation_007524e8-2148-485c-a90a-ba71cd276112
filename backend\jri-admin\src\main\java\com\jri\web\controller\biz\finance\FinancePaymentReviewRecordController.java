package com.jri.web.controller.biz.finance;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.request.finance.FinancePaymentReviewRecordAuditForm;
import com.jri.biz.domain.request.finance.FinancePaymentReviewRecordForm;
import com.jri.biz.domain.request.finance.FinancePaymentReviewRecordQuery;
import com.jri.biz.domain.request.finance.FinancePaymentReviewRecordRectifyForm;
import com.jri.biz.domain.vo.finance.FinancePaymentReviewRecordListVO;
import com.jri.biz.domain.vo.finance.FinancePaymentReviewRecordVO;
import com.jri.biz.service.finance.FinancePaymentReviewRecordService;
import com.jri.common.core.domain.R;
import com.jri.common.exception.ServiceException;
import com.jri.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * <p>
 * 审账记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Validated
@RestController
@RequestMapping("/financePaymentReviewRecord")
@Api(tags = "审账记录")
public class FinancePaymentReviewRecordController {

    @Resource
    private FinancePaymentReviewRecordService financePaymentReviewRecordService;

    @GetMapping("/auditList")
    @ApiOperation("审批列表查询")
    @PreAuthorize("@ss.hasPermi('finance:payment:review:auditList')")
    public R<IPage<FinancePaymentReviewRecordListVO>> auditList(FinancePaymentReviewRecordQuery query) {
        return R.ok(financePaymentReviewRecordService.auditList(query));
    }

    @GetMapping("/rectifyList")
    @ApiOperation("办理列表查询")
    public R<IPage<FinancePaymentReviewRecordListVO>> rectifyList(FinancePaymentReviewRecordQuery query) {
        return R.ok(financePaymentReviewRecordService.rectifyList(query));
    }

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<FinancePaymentReviewRecordVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(financePaymentReviewRecordService.getDetailById(id));
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation("保存数据")
    public R<Void> saveOrUpdate(@RequestBody @Valid FinancePaymentReviewRecordForm form) {
        financePaymentReviewRecordService.saveOrUpdate(form);
        return R.ok();
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(financePaymentReviewRecordService.deleteById(id));
    }

    @PostMapping("/audit")
    @ApiOperation("审核")
    public R<Void> audit(@RequestBody @Valid FinancePaymentReviewRecordAuditForm form) {
        financePaymentReviewRecordService.audit(form);
        return R.ok();
    }

    @PostMapping("/rectify")
    @ApiOperation("办理")
    public R<Void> rectify(@RequestBody @Valid FinancePaymentReviewRecordRectifyForm form) {
        financePaymentReviewRecordService.rectify(form);
        return R.ok();
    }

    @PostMapping("/export")
    @ApiOperation("导出")
    public void export(HttpServletResponse response, @Validated @RequestBody FinancePaymentReviewRecordQuery query) {
        var recordList = financePaymentReviewRecordService.auditExportList(query);
        if (ObjectUtil.isEmpty(recordList)) {
            throw new ServiceException("没有数据可导出");
        }
        ExcelUtil<FinancePaymentReviewRecordListVO> util = new ExcelUtil<>(FinancePaymentReviewRecordListVO.class);
        util.exportExcel(response, recordList, "审帐记录");
    }
}

