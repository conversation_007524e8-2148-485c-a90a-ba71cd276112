package com.jri.web.controller.biz.cus;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.cus.domain.entity.CusSourceDirect;
import com.jri.biz.cus.domain.request.CusSourceBaseForm;
import com.jri.biz.cus.domain.request.CusSourceDirectForm;
import com.jri.biz.cus.domain.request.CusSourceDirectQuery;
import com.jri.biz.cus.domain.request.CusSourceQuery;
import com.jri.biz.cus.domain.vo.CusSourceListVO;
import com.jri.biz.cus.domain.vo.CusSourceVO;
import com.jri.biz.cus.service.CusSourceDirectService;
import com.jri.biz.cus.service.CusSourceService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 客资来源 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
@Validated
@RestController
@RequestMapping("/cusSource")
@Api(tags = "客资来源")
public class CusSourceController {
    @Resource
    private CusSourceService cusSourceService;

    @Resource
    private CusSourceDirectService cusSourceDirectService;


    @PostMapping("/saveOrUpdate")
    @ApiOperation("保存修改")
    public R<Void> save(@RequestBody @Valid CusSourceBaseForm form) {
        cusSourceService.saveOrUpdate(form);
        return R.ok();
    }

    @GetMapping("/tree")
    @ApiOperation("树结构查询")
    public R<List<CusSourceListVO>> tree(CusSourceQuery query) {
        return R.ok(cusSourceService.tree(query));
    }

    @GetMapping("/detail")
    @ApiOperation("详情")
    public R<CusSourceVO> detail(Long id) {
        return R.ok(cusSourceService.getDetailById(id));
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(cusSourceService.deleteById(id));
    }

    @GetMapping("/direct/list")
    @ApiOperation("直投列表查询")
    public R<IPage<CusSourceDirect>> listPage(CusSourceDirectQuery query) {
        return R.ok(cusSourceDirectService.listPage(query));
    }

    @PostMapping("/direct/save")
    @ApiOperation("直投保存数据")
    public R<Void> save(@RequestBody @Valid CusSourceDirectForm form) {
        cusSourceDirectService.add(form);
        return R.ok();
    }

    @PostMapping("/enable")
    @ApiOperation("状态设置")
    public R<Void> enable(@RequestParam("id") Long id) {
        cusSourceService.enable(id);
        return R.ok();
    }

}

